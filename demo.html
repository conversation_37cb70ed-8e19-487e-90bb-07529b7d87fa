<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>关键词搜索预览</title>
    <script src="https://cdn.sheetjs.com/xlsx-0.19.3/package/dist/xlsx.full.min.js"></script>
    <style>
        .container {
            display: flex;
            height: 100vh;
        }

        .keywords {
            width: 50%;
            padding: 20px;
            border-right: 1px solid #ccc;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .preview {
            width: 50%;
            padding: 20px;
        }

        .keyword-item {
            margin: 10px 0;
            padding: 10px;
            background-color: #f0f0f0;
            cursor: pointer;
            display: flex;
            align-items: flex-start;
            position: relative;
            border: 1px solid #d9d9d9;
        }

        .keyword-item:hover {
            background-color: #e0e0e0;
        }

        .keyword-item.kept {
            background-color: #e6f7e6;
            border: 1px solid #52c41a;
        }

        .keyword-item.discarded {
            background-color: #fff1f0;
            border: 1px solid #ff4d4f;
            opacity: 0.7;
        }

        .keyword-content {
            flex: 1;
            margin-right: 100px;
            /* 为按钮留出空间 */
        }

        .action-buttons {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 10px;
        }

        .keep-btn {
            background-color: #52c41a;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
        }

        .discard-btn {
            background-color: #ff4d4f;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
        }

        .status-counts {
            display: flex;
            gap: 20px;
            margin-left: auto;
            color: #666;
        }

        .kept-count {
            color: #52c41a;
        }

        .discarded-count {
            color: #ff4d4f;
        }

        #searchFrame {
            width: 100%;
            height: 90vh;
            border: none;
        }

        .file-input {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .keyword-info {
            font-size: 12px;
            color: #666;
            height: 20px;
            margin-top: 8px;
        }

        .actions-bar {
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
            margin-bottom: 10px;
            display: flex;
            gap: 10px;
        }

        .keyword-list-container {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }

        button {
            padding: 5px 10px;
            cursor: pointer;
        }

        .selected-count {
            margin-left: auto;
            color: #666;
        }

        .exclude-words-section {
            flex: 1;
            margin: 0;
            padding: 0 15px;
            border-left: 1px solid #ddd;
            border-right: 1px solid #ddd;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .exclude-words-section h3 {
            margin: 0 0 5px 0;
            font-size: 14px;
        }

        .exclude-input {
            display: flex;
            gap: 10px;
            align-items: flex-start;
        }

        .exclude-input textarea {
            height: 32px;
            min-height: 32px;
            padding: 5px 8px;
        }

        .keyword-item.excluded {
            background-color: #f5f5f5;
            border: 1px solid #d9d9d9;
            opacity: 0.5;
        }

        .excluded-count {
            color: #666;
        }

        .exclude-tag {
            display: inline-block;
            padding: 2px 6px;
            background-color: #ff9800;
            color: white;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 8px;
        }

        .filter-toggles {
            display: flex;
            gap: 15px;
            margin: 0 20px;
        }

        .toggle-label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .toggle-text {
            margin-left: 5px;
            font-size: 14px;
        }

        .toggle-text.kept {
            color: #52c41a;
        }

        .toggle-text.discarded {
            color: #ff4d4f;
        }

        .toggle-text.excluded {
            color: #666;
        }

        .toggle-text.untreated {
            color: #1890ff;
        }

        .keyword-item.hidden {
            display: none;
        }

        .config-buttons {
            display: flex;
            gap: 10px;
        }

        .config-buttons button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 4px;
        }

        .config-buttons button:hover {
            background-color: #40a9ff;
        }

        .top-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
        }

        .export-kept-btn {
            white-space: nowrap;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fff;
            margin: 10% auto;
            padding: 20px;
            border-radius: 8px;
            width: 500px;
            max-width: 90%;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close {
            font-size: 24px;
            cursor: pointer;
        }

        .exclude-word-input {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .exclude-word-input input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .exclude-words-table {
            max-height: 300px;
            overflow-y: auto;
        }

        .exclude-words-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .exclude-words-table th,
        .exclude-words-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .exclude-words-table th {
            background-color: #f5f5f5;
        }

        .modal-footer {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .delete-btn {
            color: #ff4d4f;
            cursor: pointer;
        }

        .clear-btn {
            background-color: #ff4d4f !important;
        }

        .clear-btn:hover {
            background-color: #ff7875 !important;
        }

        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .stats-info {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .stats-info div {
            margin: 5px 0;
            font-size: 14px;
        }

        .filter-input {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }

        .filter-input input {
            width: 100px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        [translate="yes"] {
            visibility: hidden;
            height: 0;
            overflow: hidden;
            /* 防止内容溢出 */
            transition: all 0.3s ease-in-out;
            /* 添加过渡效果 */
        }

        [translate="yes"] {
            visibility: hidden;
            height: 0;
            overflow: hidden;
            transition: visibility 0.3s, height 0.3s;
        }

        /* 当元素包含谷歌翻译生成的font元素时显示 */
        [translate="yes"]:has(font[style*="vertical-align: inherit;"]) {
            visibility: visible;
            height: auto;
        }

        /* 确保翻译后的文本正确显示 */
        [translate="yes"] font[style*="vertical-align: inherit;"] {
            display: block;
        }

        .filtered-count {
            color: #1890ff;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="keywords">
            <h2>关键词列表</h2>
            <div class="top-controls">
                <div class="file-input">
                    <input type="file" id="fileInput" accept=".xls,.xlsx" onchange="processFile()">
                </div>
                <div class="exclude-words-section">
                    <button onclick="showExcludeWordsModal()">管理排除词</button>
                </div>
                <div class="config-buttons">
                    <button onclick="exportConfig()">导出配置</button>
                    <input type="file" id="configFile" accept=".json" style="display: none;"
                        onchange="importConfig(event)">
                    <button onclick="document.getElementById('configFile').click()">导入配置</button>
                    <button onclick="clearAllConfig()" class="clear-btn">清除配置</button>
                    <button onclick="showSearchIndexFilterModal()">搜索指数过滤</button>
                </div>
                <button class="export-kept-btn" onclick="exportKept()">导出已保留关键词</button>
            </div>
            <div class="actions-bar">
                <div class="filter-toggles">
                    <label class="toggle-label">
                        <input type="checkbox" id="showKept" checked onchange="updateKeywordList()">
                        <span class="toggle-text kept">显示已保留</span>
                    </label>
                    <label class="toggle-label">
                        <input type="checkbox" id="showDiscarded" checked onchange="updateKeywordList()">
                        <span class="toggle-text discarded">显示已丢弃</span>
                    </label>
                    <label class="toggle-label">
                        <input type="checkbox" id="showExcluded" checked onchange="updateKeywordList()">
                        <span class="toggle-text excluded">显示已排除</span>
                    </label>
                    <label class="toggle-label">
                        <input type="checkbox" id="showUntreated" checked onchange="updateKeywordList()">
                        <span class="toggle-text untreated">显示未处理</span>
                    </label>
                </div>
                <div class="status-counts">
                    <span class="kept-count">已保留: 0</span>
                    <span class="discarded-count">已丢弃: 0</span>
                    <span class="excluded-count">已排除: 0</span>
                    <span class="filtered-count">已过滤: 0</span>
                </div>
            </div>
            <div id="keywordList" class="keyword-list-container">
                <!-- 关键词将通过JavaScript动态添加 -->
            </div>
        </div>
        <div class="preview">
            <h2>图片搜索预览</h2>
            <iframe id="searchFrame" src="https://www.google.com/search?tbm=isch&igu=1"></iframe>
        </div>
    </div>

    <div id="excludeWordsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>管理排除词</h3>
                <span class="close" onclick="closeExcludeWordsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="exclude-word-input">
                    <input type="text" id="newExcludeWord" placeholder="输入新的排除词">
                    <button onclick="addExcludeWord()">添加</button>
                </div>
                <div class="exclude-words-table">
                    <table>
                        <thead>
                            <tr>
                                <th>排除词</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="excludeWordsTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button onclick="saveExcludeWords()">保存</button>
                <button onclick="closeExcludeWordsModal()">取消</button>
            </div>
        </div>
    </div>

    <div id="searchIndexFilterModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>搜索指数过滤配置</h3>
                <span class="close" onclick="closeSearchIndexFilterModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="stats-info">
                    <div>平均数: <span id="avgSearchIndex">-</span></div>
                    <div>中位数: <span id="medianSearchIndex">-</span></div>
                    <div>众数: <span id="modeSearchIndex">-</span></div>
                </div>
                <div class="filter-input">
                    <label>过滤搜索指数小于或等于：</label>
                    <input type="number" id="minSearchIndex" min="0" step="1">
                </div>
            </div>
            <div class="modal-footer">
                <button onclick="applySearchIndexFilter()">应用</button>
                <button onclick="closeSearchIndexFilterModal()">取消</button>
            </div>
        </div>
    </div>

    <script>
        let keywordsData = []; // 存储所有关键词数据
        let keptKeywords = new Set(); // 存储保留的关键词
        let discardedKeywords = new Set(); // 存储丢弃的关键词
        let excludeWords = new Set(); // 存储排除词
        let tempExcludeWords = new Set(); // 临时存储编辑中的排除词
        let minSearchIndex = 0; // 存储搜索指数过滤阈值
        let currentFileName = ''; // 存储当前打开的文件名

        // 从localStorage加载状态
        function loadState() {
            const savedKept = localStorage.getItem('keptKeywords');
            const savedDiscarded = localStorage.getItem('discardedKeywords');
            const savedExcludeWords = localStorage.getItem('excludeWords');

            if (savedKept) keptKeywords = new Set(JSON.parse(savedKept));
            if (savedDiscarded) discardedKeywords = new Set(JSON.parse(savedDiscarded));
            if (savedExcludeWords) {
                excludeWords = new Set(JSON.parse(savedExcludeWords));
            }

            // 加载筛选器状态
            const savedFilterState = localStorage.getItem('filterState');
            if (savedFilterState) {
                const filterState = JSON.parse(savedFilterState);
                document.getElementById('showKept').checked = filterState.showKept;
                document.getElementById('showDiscarded').checked = filterState.showDiscarded;
                document.getElementById('showExcluded').checked = filterState.showExcluded;
                document.getElementById('showUntreated').checked = filterState.showUntreated ?? true;
            }

            const savedMinSearchIndex = localStorage.getItem('minSearchIndex');
            if (savedMinSearchIndex) {
                minSearchIndex = parseInt(savedMinSearchIndex);
            }
        }

        // 保存状态到localStorage
        function saveState() {
            localStorage.setItem('keptKeywords', JSON.stringify([...keptKeywords]));
            localStorage.setItem('discardedKeywords', JSON.stringify([...discardedKeywords]));
            localStorage.setItem('excludeWords', JSON.stringify([...excludeWords]));
            updateCounts();
        }

        function updateCounts() {
            document.querySelector('.kept-count').textContent = `已保留: ${keptKeywords.size}`;
            document.querySelector('.discarded-count').textContent = `已丢弃: ${discardedKeywords.size}`;
            document.querySelector('.excluded-count').textContent = `已排除: ${getExcludedCount()}`;
            document.querySelector('.filtered-count').textContent = `已过滤: ${getFilteredCount()}`;
        }

        function getExcludedCount() {
            return keywordsData.filter(row =>
                row && row[0] && isExcluded(row[0].trim())
            ).length;
        }

        function getFilteredCount() {
            // 只统计因搜索指数过滤而被排除的关键词数量
            return keywordsData.filter(row =>
                row && row[0] && row[1] && 
                minSearchIndex > 0 && 
                parseInt(row[1]) <= minSearchIndex
            ).length;
        }

        function showExcludeWordsModal() {
            const modal = document.getElementById('excludeWordsModal');
            modal.style.display = 'block';

            // 初始化临时排除词集合
            tempExcludeWords = new Set(excludeWords);
            updateExcludeWordsTable();
        }

        function closeExcludeWordsModal() {
            const modal = document.getElementById('excludeWordsModal');
            modal.style.display = 'none';
        }

        function addExcludeWord() {
            const input = document.getElementById('newExcludeWord');
            const word = input.value.trim();

            if (word) {
                tempExcludeWords.add(word);
                input.value = '';
                updateExcludeWordsTable();
            }
        }

        function deleteExcludeWord(word) {
            tempExcludeWords.delete(word);
            updateExcludeWordsTable();
        }

        function updateExcludeWordsTable() {
            const tbody = document.getElementById('excludeWordsTableBody');
            tbody.innerHTML = '';

            Array.from(tempExcludeWords).sort().forEach(word => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${word}</td>
                    <td>
                        <span class="delete-btn" onclick="deleteExcludeWord('${word}')">删除</span>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        function saveExcludeWords() {
            excludeWords = new Set(tempExcludeWords);

            // 将被排除的关键词从保留和丢弃集合中移除
            keywordsData.forEach(row => {
                if (row && row[0]) {
                    const keyword = row[0].trim();
                    if (isExcluded(keyword)) {
                        keptKeywords.delete(keyword);
                        discardedKeywords.delete(keyword);
                    }
                }
            });

            saveState();
            updateKeywordList();
            closeExcludeWordsModal();
        }

        // 添加点击模态框外部关闭功能
        window.onclick = function (event) {
            const modal = document.getElementById('excludeWordsModal');
            if (event.target === modal) {
                closeExcludeWordsModal();
            }
        }

        // 添加回车键添加排除词功能
        document.getElementById('newExcludeWord').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                addExcludeWord();
            }
        });

        function isExcluded(keyword, searchIndex) {
            // 检查搜索指数
            if (searchIndex && minSearchIndex > 0 && parseInt(searchIndex) <= minSearchIndex) {
                return true;
            }

            // 将关键词转换为小写并分割成单词
            const keywordLower = keyword.toLowerCase();
            const keywordWords = keywordLower
                .replace(/[.,/#!$%^&*;:{}=\-_`~()]/g, ' ')
                .split(' ')
                .filter(word => word.length > 0);

            // 检查每个排除词
            return Array.from(excludeWords).some(excludeWord => {
                const trimmedExcludeWord = excludeWord.trim().toLowerCase();

                // 如果排除词包含空格，作为短语进行匹配
                if (trimmedExcludeWord.includes(' ')) {
                    // 将排除短语分割成单词数组
                    const excludeWordParts = trimmedExcludeWord.split(' ').filter(word => word.length > 0);

                    // 在关键词中查找连续的匹配
                    for (let i = 0; i <= keywordWords.length - excludeWordParts.length; i++) {
                        let match = true;
                        for (let j = 0; j < excludeWordParts.length; j++) {
                            if (keywordWords[i + j] !== excludeWordParts[j]) {
                                match = false;
                                break;
                            }
                        }
                        if (match) return true;
                    }
                    return false;
                } else {
                    // 单个词的情况，检查是否作为独立单词存在
                    return keywordWords.includes(trimmedExcludeWord);
                }
            });
        }

        function keepKeyword(keyword, element) {
            if (isExcluded(keyword)) {
                alert('包含排除词的关键词不能被保留！');
                return;
            }
            discardedKeywords.delete(keyword);
            keptKeywords.add(keyword);
            element.classList.remove('discarded');
            element.classList.add('kept');
            saveState();
        }

        function discardKeyword(keyword, element) {
            keptKeywords.delete(keyword);
            discardedKeywords.add(keyword);
            element.classList.remove('kept');
            element.classList.add('discarded');
            saveState();
        }

        function exportKept() {
            if (keptKeywords.size === 0) {
                alert('没有已保留的关键词');
                return;
            }

            const selectedData = keywordsData.filter(row =>
                row && row[0] && keptKeywords.has(row[0].trim()) &&
                !isExcluded(row[0].trim(), row[1])
            );

            const headers = ['关键词', '搜索指数', '搜索涨幅', '点击率', '点击率涨幅', '卖家规模指数', '卖家规模指数涨幅'];
            const exportData = [headers, ...selectedData];

            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(exportData);
            XLSX.utils.book_append_sheet(wb, ws, "Kept Keywords");
            const exportFileName = currentFileName ? 
                `kept_${currentFileName}.xlsx` : 
                'kept_keywords.xlsx';
            XLSX.writeFile(wb, exportFileName);
        }

        function updateKeywordList() {
            const keywordList = document.getElementById('keywordList');
            keywordList.innerHTML = '';

            // 获取显示设置
            const showKept = document.getElementById('showKept').checked;
            const showDiscarded = document.getElementById('showDiscarded').checked;
            const showExcluded = document.getElementById('showExcluded').checked;
            const showUntreated = document.getElementById('showUntreated').checked;

            keywordsData.forEach(row => {
                if (row && row[0]) {
                    const keyword = row[0].trim();
                    const searchIndex = row[1];
                    const keywordDiv = document.createElement('div');
                    keywordDiv.className = 'keyword-item';

                    const excluded = isExcluded(keyword, searchIndex);
                    let shouldShow = true;

                    if (excluded) {
                        keywordDiv.classList.add('excluded');
                        shouldShow = showExcluded;
                    } else if (keptKeywords.has(keyword)) {
                        keywordDiv.classList.add('kept');
                        shouldShow = showKept;
                    } else if (discardedKeywords.has(keyword)) {
                        keywordDiv.classList.add('discarded');
                        shouldShow = showDiscarded;
                    } else {
                        shouldShow = showUntreated;
                    }

                    if (!shouldShow) {
                        keywordDiv.classList.add('hidden');
                    }

                    keywordDiv.innerHTML = `
                        <div class="keyword-content">
                            <div style="font-size: 16px; margin-bottom: 8px;" translate="no">
                                ${keyword}
                            </div>
                            <div translate="yes" style="color: #666; margin: 4px 0;">${keyword}</div>
                            <div class="keyword-info">
                                ${excluded ? '<span class="exclude-tag">已排除</span>' : ''}
                                搜索指数: ${row[1]} (${row[2]}) | 
                                点击率: ${row[3]} (${row[4]}) | 
                                卖家规模指数: ${row[5]} (${row[6]})
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button class="keep-btn" onclick="keepKeyword('${keyword}', this.closest('.keyword-item'))" ${excluded ? 'disabled' : ''}>保留</button>
                            <button class="discard-btn" onclick="discardKeyword('${keyword}', this.closest('.keyword-item'))" ${excluded ? 'disabled' : ''}>丢弃</button>
                        </div>
                    `;

                    keywordDiv.addEventListener('click', (e) => {
                        if (!e.target.classList.contains('keep-btn') && !e.target.classList.contains('discard-btn')) {
                            updateSearch(keyword);
                        }
                    });

                    keywordList.appendChild(keywordDiv);
                }
            });
            updateCounts();
        }

        function processFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (file) {
                currentFileName = file.name.replace(/\.[^/.]+$/, '');
                
                const reader = new FileReader();
                reader.onload = function (e) {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });

                    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                    const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });

                    keywordsData = jsonData.slice(6);
                    loadState();
                    updateKeywordList();
                };
                reader.readAsArrayBuffer(file);
            }
        }

        function updateSearch(keyword) {
            const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(keyword)}&tbm=isch&igu=1`;
            document.getElementById('searchFrame').src = searchUrl;
        }

        // 添加保存显示设置到 localStorage 的功能
        function saveFilterState() {
            const filterState = {
                showKept: document.getElementById('showKept').checked,
                showDiscarded: document.getElementById('showDiscarded').checked,
                showExcluded: document.getElementById('showExcluded').checked,
                showUntreated: document.getElementById('showUntreated').checked
            };
            localStorage.setItem('filterState', JSON.stringify(filterState));
        }

        // 修改筛选器的 change 事件处理
        document.querySelectorAll('.filter-toggles input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                saveFilterState();
                updateKeywordList();
            });
        });

        // 页面加载时加载保存的状态
        loadState();

        // 导出配置
        function exportConfig() {
            const config = {
                excludeWords: Array.from(excludeWords),
                keptKeywords: Array.from(keptKeywords),
                discardedKeywords: Array.from(discardedKeywords),
                minSearchIndex: minSearchIndex,
                filterState: {
                    showKept: document.getElementById('showKept').checked,
                    showDiscarded: document.getElementById('showDiscarded').checked,
                    showExcluded: document.getElementById('showExcluded').checked,
                    showUntreated: document.getElementById('showUntreated').checked
                }
            };

            // 创建并下载配置文件
            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            const configFileName = currentFileName ? 
                `kept_${currentFileName}_config.json` : 
                'kept_keywords_config.json';
            a.download = configFileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 导入配置
        function importConfig(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    try {
                        const config = JSON.parse(e.target.result);

                        // 更新排除词
                        excludeWords = new Set(config.excludeWords || []);

                        // 更新关键词状态
                        keptKeywords = new Set(config.keptKeywords || []);
                        discardedKeywords = new Set(config.discardedKeywords || []);

                        // 更新搜索指数过滤值
                        minSearchIndex = config.minSearchIndex || 0;
                        localStorage.setItem('minSearchIndex', minSearchIndex);

                        // 更新筛选器状态
                        if (config.filterState) {
                            document.getElementById('showKept').checked = config.filterState.showKept;
                            document.getElementById('showDiscarded').checked = config.filterState.showDiscarded;
                            document.getElementById('showExcluded').checked = config.filterState.showExcluded;
                            document.getElementById('showUntreated').checked = config.filterState.showUntreated ?? true;
                        }

                        // 保存新配置到 localStorage
                        saveState();
                        saveFilterState();

                        // 更新界面
                        updateKeywordList();

                        alert('配置导入成功！');
                    } catch (error) {
                        alert('配置文件格式错误！');
                        console.error('配置导入错误:', error);
                    }
                };
                reader.readAsText(file);
            }
            event.target.value = '';
        }

        function clearAllConfig() {
            if (confirm('确定要清除所有配置吗？这将清除所有保留/丢弃的关键词状态和排除词设置。')) {
                // 清除所有数据
                keptKeywords = new Set();
                discardedKeywords = new Set();
                excludeWords = new Set();
                minSearchIndex = 0;

                // 清除 localStorage
                localStorage.removeItem('keptKeywords');
                localStorage.removeItem('discardedKeywords');
                localStorage.removeItem('excludeWords');
                localStorage.removeItem('filterState');
                localStorage.removeItem('minSearchIndex');

                // 重置筛选器状态
                document.getElementById('showKept').checked = true;
                document.getElementById('showDiscarded').checked = true;
                document.getElementById('showExcluded').checked = true;
                document.getElementById('showUntreated').checked = true;

                // 更新界面
                updateKeywordList();
                alert('所有配置已清除');
            }
        }

        function showSearchIndexFilterModal() {
            const modal = document.getElementById('searchIndexFilterModal');
            modal.style.display = 'block';

            // 计算统计数据
            const searchIndices = keywordsData
                .filter(row => row && row[1])
                .map(row => parseInt(row[1]))
                .filter(val => !isNaN(val));

            if (searchIndices.length > 0) {
                // 计算平均数
                const avg = searchIndices.reduce((a, b) => a + b) / searchIndices.length;
                document.getElementById('avgSearchIndex').textContent = Math.round(avg);

                // 计算中位数
                const sorted = [...searchIndices].sort((a, b) => a - b);
                const mid = Math.floor(sorted.length / 2);
                const median = sorted.length % 2 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2;
                document.getElementById('medianSearchIndex').textContent = Math.round(median);

                // 计算众数
                const counts = {};
                let maxCount = 0;
                let mode = null;
                searchIndices.forEach(num => {
                    counts[num] = (counts[num] || 0) + 1;
                    if (counts[num] > maxCount) {
                        maxCount = counts[num];
                        mode = num;
                    }
                });
                document.getElementById('modeSearchIndex').textContent = mode;

                // 设置当前过滤值
                document.getElementById('minSearchIndex').value = minSearchIndex || '';
            }
        }

        function closeSearchIndexFilterModal() {
            const modal = document.getElementById('searchIndexFilterModal');
            modal.style.display = 'none';
        }

        function applySearchIndexFilter() {
            const input = document.getElementById('minSearchIndex');
            minSearchIndex = parseInt(input.value) || 0;

            // 保存配置
            localStorage.setItem('minSearchIndex', minSearchIndex);

            // 更新界面
            updateKeywordList();
            closeSearchIndexFilterModal();
        }
    </script>
</body>

</html>